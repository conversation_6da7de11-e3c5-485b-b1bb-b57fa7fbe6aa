import { NextRequest, NextResponse } from "next/server"
import { Resend } from "resend"

const resend = new Resend(process.env.RESEND_API_KEY)

export async function GET() {
  try {
    // Verificar que la API key esté configurada
    if (!process.env.RESEND_API_KEY) {
      return NextResponse.json(
        { 
          success: false, 
          error: "RESEND_API_KEY no está configurada" 
        },
        { status: 500 }
      )
    }

    // Enviar email de prueba
    const result = await resend.emails.send({
      from: "Karedesk Test <<EMAIL>>",
      to: ["<EMAIL>"], // Email especial de Resend para testing
      subject: "🧪 Test de configuración - Karedesk",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h1 style="color: #14b8a6;">✅ Test de Email Exitoso</h1>
          <p>Este es un email de prueba para verificar que la configuración de Resend está funcionando correctamente.</p>
          <div style="background-color: #f0f9ff; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3>Detalles de la configuración:</h3>
            <ul>
              <li><strong>API Key:</strong> Configurada ✅</li>
              <li><strong>Servicio:</strong> Resend</li>
              <li><strong>Proyecto:</strong> Karedesk Website</li>
              <li><strong>Fecha:</strong> ${new Date().toLocaleString('es-ES')}</li>
            </ul>
          </div>
          <p style="color: #666;">Si recibes este email, significa que el sistema de emails está funcionando correctamente.</p>
        </div>
      `,
    })

    return NextResponse.json({
      success: true,
      message: "Email de prueba enviado exitosamente",
      emailId: result.data?.id,
      details: {
        apiKeyConfigured: !!process.env.RESEND_API_KEY,
        timestamp: new Date().toISOString(),
      }
    })

  } catch (error) {
    console.error("Error en test de email:", error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        details: {
          apiKeyConfigured: !!process.env.RESEND_API_KEY,
          timestamp: new Date().toISOString(),
        }
      },
      { status: 500 }
    )
  }
}
