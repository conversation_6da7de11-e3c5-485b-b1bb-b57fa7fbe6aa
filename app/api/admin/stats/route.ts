import { NextResponse } from "next/server"
import { getContactStats, getContactsNeedingFollowup } from "@/lib/database"

export async function GET() {
  try {
    const [stats, needingFollowup] = await Promise.all([
      getContactStats(),
      getContactsNeedingFollowup()
    ])

    return NextResponse.json({
      success: true,
      data: {
        ...stats,
        needing_followup: needingFollowup.length,
        followup_contacts: needingFollowup.slice(0, 5) // Solo los primeros 5 para el dashboard
      }
    })

  } catch (error) {
    console.error("Error fetching stats:", error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido"
      },
      { status: 500 }
    )
  }
}
