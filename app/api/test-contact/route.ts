import { NextRequest, NextResponse } from "next/server"
import { submitContactForm, type ContactFormState } from "@/lib/contact-actions"

export async function POST(request: NextRequest) {
  try {
    // Datos de prueba
    const testData = {
      name: "<PERSON>",
      email: "<EMAIL>", // Email especial de Resend para testing
      phone: "+1234567890",
      company: "Empresa Test",
      service: "Asistencia Informática",
      message: "Este es un mensaje de prueba para verificar que el sistema de emails funciona correctamente. Necesitamos soporte técnico para nuestros servidores.",
      budget: "5000-10000",
      timeline: "1-mes"
    }

    // Crear FormData
    const formData = new FormData()
    Object.entries(testData).forEach(([key, value]) => {
      formData.append(key, value)
    })

    // Estado inicial
    const initialState: ContactFormState = {}

    // Llamar a la función de submit
    const result = await submitContactForm(initialState, formData)

    return NextResponse.json({
      success: true,
      message: "Test de formulario de contacto ejecutado",
      result: result,
      testData: testData
    })

  } catch (error) {
    console.error("Error en test de contacto:", error)
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    )
  }
}
