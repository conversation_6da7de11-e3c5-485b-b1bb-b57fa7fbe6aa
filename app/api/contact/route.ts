import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { saveContact } from "@/lib/database"
import { sendContactEmails } from "@/lib/email-service"

const contactSchema = z.object({
  name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
  email: z.string().email("Email inválido"),
  phone: z
    .string()
    .optional()
    .transform((v) => {
      if (!v || v.trim() === "") return undefined
      // Limpiar el número y permitir formato español
      return v.replace(/\D/g, "")
    })
    .refine((v) => {
      if (!v) return true // Opcional
      // Validar números españoles: 9 dígitos que empiecen por 6, 7, 8 o 9
      return /^[6789]\d{8}$/.test(v) || /^34[6789]\d{8}$/.test(v)
    }, {
      message: "Introduce un número de teléfono español válido (ej: *********)",
    }),
  company: z.string().optional(),
  service: z.string().min(1, "El servicio es requerido"),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  message: z
    .string()
    .trim()
    .min(10, "El mensaje debe tener al menos 10 caracteres"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validar los datos
    const validatedData = contactSchema.parse(body)
    
    // Guardar en la base de datos
    const contact = await saveContact(validatedData)
    
    // Enviar emails
    try {
      await sendContactEmails(contact)
    } catch (emailError) {
      console.error("Error enviando emails:", emailError)
      // No fallar la request si los emails fallan
    }
    
    return NextResponse.json({
      success: true,
      message: "Consulta enviada exitosamente",
      data: {
        id: contact.id,
        email: contact.email,
        service: contact.service
      }
    })
    
  } catch (error) {
    console.error("Error en /api/contact:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Datos inválidos",
          errors: error.errors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      {
        success: false,
        message: "Error interno del servidor"
      },
      { status: 500 }
    )
  }
}
