import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { saveContact } from "@/lib/database"
import { sendContactEmails } from "@/lib/email-service"

const contactSchema = z.object({
  name: z.string().min(1, "El nombre es requerido"),
  email: z.string().email("Email inválido"),
  phone: z.string().min(1, "El teléfono es requerido"),
  company: z.string().optional(),
  service: z.string().min(1, "El servicio es requerido"),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  message: z.string().min(1, "El mensaje es requerido"),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validar los datos
    const validatedData = contactSchema.parse(body)
    
    // Guardar en la base de datos
    const contact = await saveContact(validatedData)
    
    // Enviar emails
    try {
      await sendContactEmails(contact)
    } catch (emailError) {
      console.error("Error enviando emails:", emailError)
      // No fallar la request si los emails fallan
    }
    
    return NextResponse.json({
      success: true,
      message: "Consulta enviada exitosamente",
      data: {
        id: contact.id,
        email: contact.email,
        service: contact.service
      }
    })
    
  } catch (error) {
    console.error("Error en /api/contact:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Datos inválidos",
          errors: error.errors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      {
        success: false,
        message: "Error interno del servidor"
      },
      { status: 500 }
    )
  }
}
