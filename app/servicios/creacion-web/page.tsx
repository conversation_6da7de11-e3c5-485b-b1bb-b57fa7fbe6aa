"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  ArrowLeft,
  CheckCircle,
  Code,
  Smartphone,
  Search,
  ShoppingCart,
  Palette,
  Zap,
  Globe,
  Monitor,
  Layers,
  Settings,
  Star,
  TrendingUp,
  Rocket,
} from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { motion } from "framer-motion"
import ContactForm from "@/components/contact-form"

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6, ease: "easeOut" },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

export default function CreacionWeb() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="fixed top-0 w-full z-50 bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center hover:scale-105 transition-transform">
            <Image
              src="/logo.png"
              alt="Karedesk"
              width={48}
              height={48}
              className="w-12 h-12"
            />
          </Link>
          <Link href="/">
            <Button variant="ghost" className="text-slate-300 hover:text-white rounded-2xl">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Volver al inicio
            </Button>
          </Link>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-32 pb-20 px-4">
        <div className="container mx-auto">
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <motion.div
              className="w-24 h-24 bg-emerald-600/20 rounded-3xl flex items-center justify-center mx-auto mb-8"
              whileHover={{ rotate: 360, scale: 1.1 }}
              transition={{ duration: 0.6 }}
            >
              <Code className="w-12 h-12 text-emerald-400" />
            </motion.div>
            <h1 className="text-5xl md:text-7xl font-bold text-white mb-6 tracking-tight">
              Creación de
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-emerald-400 to-teal-400">
                Páginas Web
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto font-light leading-relaxed">
              Desarrollamos sitios web modernos, rápidos y optimizados que convierten visitantes en clientes y impulsan
              tu crecimiento digital
            </p>
          </motion.div>
        </div>
      </section>

      {/* Services Overview */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Tipos de Sitios Web que Creamos
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Desde landing pages hasta e-commerce complejos, desarrollamos la solución perfecta para tu negocio
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Rocket,
                title: "Landing Pages",
                description: "Páginas de aterrizaje optimizadas para conversión y generación de leads",
                features: ["Alta conversión", "Carga ultrarrápida", "A/B testing"],
                color: "emerald",
              },
              {
                icon: ShoppingCart,
                title: "E-commerce",
                description: "Tiendas online completas con pasarelas de pago y gestión de inventario",
                features: ["Carrito de compras", "Pagos seguros", "Gestión de productos"],
                color: "blue",
              },
              {
                icon: Globe,
                title: "Sitios Corporativos",
                description: "Presencia web profesional que refleja la identidad de tu empresa",
                features: ["Diseño profesional", "Múltiples secciones", "Panel de administración"],
                color: "purple",
              },
              {
                icon: Monitor,
                title: "Aplicaciones Web",
                description: "Sistemas web personalizados para optimizar tus procesos internos",
                features: ["Funcionalidad avanzada", "Integración de APIs", "Escalabilidad"],
                color: "orange",
              },
              {
                icon: Layers,
                title: "Portafolios Digitales",
                description: "Showcases visuales para profesionales creativos y agencias",
                features: ["Galería interactiva", "Diseño visual", "Optimización móvil"],
                color: "pink",
              },
              {
                icon: Settings,
                title: "Sistemas CMS",
                description: "Gestores de contenido personalizados para fácil administración",
                features: ["Editor intuitivo", "Gestión de usuarios", "SEO integrado"],
                color: "indigo",
              },
            ].map((service, index) => (
              <motion.div
                key={service.title}
                variants={fadeInUp}
                whileHover={{ y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 hover:border-emerald-500 transition-all duration-300 h-full rounded-3xl">
                  <CardHeader className="p-8">
                    <motion.div
                      className={`w-16 h-16 bg-${service.color}-600/20 rounded-2xl flex items-center justify-center mb-6`}
                      whileHover={{ rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <service.icon className={`w-8 h-8 text-${service.color}-400`} />
                    </motion.div>
                    <CardTitle className="text-white text-xl font-bold mb-3 tracking-tight">{service.title}</CardTitle>
                    <CardDescription className="text-slate-400 font-light leading-relaxed mb-6">
                      {service.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="px-8 pb-8">
                    <ul className="space-y-3">
                      {service.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-center text-slate-300 font-medium">
                          <CheckCircle className={`w-4 h-4 text-${service.color}-400 mr-3`} />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Development Process */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Proceso de Desarrollo</h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Metodología ágil que garantiza resultados excepcionales en tiempo y forma
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Análisis y Planificación",
                description: "Definimos objetivos, audiencia y funcionalidades necesarias",
                icon: Search,
              },
              {
                step: "02",
                title: "Diseño UX/UI",
                description: "Creamos wireframes y diseños visuales centrados en el usuario",
                icon: Palette,
              },
              {
                step: "03",
                title: "Desarrollo",
                description: "Programamos con las mejores tecnologías y prácticas del mercado",
                icon: Code,
              },
              {
                step: "04",
                title: "Lanzamiento y Optimización",
                description: "Publicamos el sitio y optimizamos basado en métricas reales",
                icon: TrendingUp,
              },
            ].map((process, index) => (
              <motion.div key={process.step} variants={fadeInUp} className="text-center">
                <Card className="bg-slate-900/50 border-slate-700 hover:border-emerald-500 transition-all duration-300 rounded-3xl">
                  <CardContent className="p-8">
                    <div className="text-6xl font-bold text-emerald-400/20 mb-4">{process.step}</div>
                    <motion.div
                      className="w-16 h-16 bg-emerald-600/20 rounded-2xl flex items-center justify-center mx-auto mb-6"
                      whileHover={{ scale: 1.1, rotate: 360 }}
                      transition={{ duration: 0.6 }}
                    >
                      <process.icon className="w-8 h-8 text-emerald-400" />
                    </motion.div>
                    <h3 className="text-white font-bold text-lg mb-3 tracking-tight">{process.title}</h3>
                    <p className="text-slate-400 font-light leading-relaxed">{process.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Features & Benefits */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            <motion.div variants={fadeInUp}>
              <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
                Características Incluidas
              </h2>
              <p className="text-xl text-slate-300 mb-8 font-light leading-relaxed">
                Todos nuestros sitios web incluyen estas características esenciales para el éxito digital
              </p>
              <div className="space-y-6">
                {[
                  {
                    title: "Diseño Responsive",
                    description: "Adaptación perfecta a todos los dispositivos y tamaños de pantalla",
                    icon: Smartphone,
                  },
                  {
                    title: "SEO Optimizado",
                    description: "Estructura y contenido optimizado para motores de búsqueda",
                    icon: Search,
                  },
                  {
                    title: "Velocidad de Carga",
                    description: "Optimización avanzada para tiempos de carga ultrarrápidos",
                    icon: Zap,
                  },
                  {
                    title: "Seguridad Avanzada",
                    description: "Certificados SSL, protección contra malware y backups automáticos",
                    icon: Settings,
                  },
                ].map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    className="flex items-start space-x-4"
                    initial={{ opacity: 0, x: -50 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.2 }}
                    whileHover={{ x: 10 }}
                  >
                    <motion.div
                      className="w-8 h-8 bg-emerald-600 rounded-2xl flex items-center justify-center flex-shrink-0 mt-1"
                      whileHover={{ scale: 1.2, rotate: 360 }}
                      transition={{ duration: 0.3 }}
                    >
                      <feature.icon className="w-5 h-5 text-white" />
                    </motion.div>
                    <div>
                      <h3 className="text-white font-bold text-lg mb-2 tracking-tight">{feature.title}</h3>
                      <p className="text-slate-400 font-light leading-relaxed">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div variants={fadeInUp}>
              <Card className="bg-gradient-to-br from-emerald-600/20 to-teal-600/20 border-slate-700 rounded-3xl">
                <CardContent className="p-8">
                  <h3 className="text-2xl font-bold text-white mb-6 tracking-tight">Resultados Garantizados</h3>
                  <div className="grid grid-cols-2 gap-6">
                    {[
                      { number: "3s", label: "Tiempo de Carga" },
                      { number: "100%", label: "Mobile Friendly" },
                      { number: "95+", label: "PageSpeed Score" },
                      { number: "24/7", label: "Soporte Técnico" },
                    ].map((stat, index) => (
                      <motion.div
                        key={stat.label}
                        className="text-center"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      >
                        <div className="text-3xl font-bold text-emerald-400 mb-2 tracking-tight">{stat.number}</div>
                        <div className="text-slate-300 font-medium">{stat.label}</div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </motion.section>

      {/* Technologies */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              Tecnologías que Utilizamos
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto font-light">
              Stack tecnológico moderno para crear sitios web rápidos, seguros y escalables
            </p>
          </motion.div>

          <div className="grid md:grid-cols-4 gap-6">
            {[
              { name: "React", category: "Frontend" },
              { name: "Next.js", category: "Framework" },
              { name: "Node.js", category: "Backend" },
              { name: "WordPress", category: "CMS" },
              { name: "Tailwind CSS", category: "Styling" },
              { name: "MongoDB", category: "Database" },
              { name: "Vercel", category: "Hosting" },
              { name: "Stripe", category: "Payments" },
            ].map((tech, index) => (
              <motion.div
                key={tech.name}
                variants={fadeInUp}
                whileHover={{ y: -5, scale: 1.05 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 hover:border-emerald-500 transition-all duration-300 rounded-2xl">
                  <CardContent className="p-6 text-center">
                    <h3 className="text-white font-bold text-lg mb-2 tracking-tight">{tech.name}</h3>
                    <Badge className="bg-emerald-600/20 text-emerald-400 border-emerald-600/30">{tech.category}</Badge>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Testimonials */}
      <motion.section
        className="py-20 px-4 bg-slate-800/50"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
      >
        <div className="container mx-auto">
          <motion.div className="text-center mb-16" variants={fadeInUp}>
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">Proyectos Exitosos</h2>
          </motion.div>

          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {[
              {
                name: "Sofia Ramírez",
                role: "Fundadora, BoutiqueFashion",
                content:
                  "Nuestro e-commerce desarrollado por Karedesk aumentó las ventas online en un 200%. El diseño es hermoso y funciona perfectamente.",
                rating: 5,
              },
              {
                name: "Diego Morales",
                role: "Director Marketing, TechStartup",
                content:
                  "La landing page que crearon convierte increíblemente bien. Pasamos de 2% a 8% de conversión en solo un mes.",
                rating: 5,
              },
            ].map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                variants={fadeInUp}
                whileHover={{ y: -5 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 h-full rounded-3xl">
                  <CardContent className="p-8">
                    <div className="flex items-center mb-6">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <p className="text-slate-300 mb-6 font-light leading-relaxed text-lg">"{testimonial.content}"</p>
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-emerald-600 rounded-2xl flex items-center justify-center mr-4">
                        <Code className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <div className="text-white font-bold tracking-tight">{testimonial.name}</div>
                        <div className="text-slate-400 font-medium">{testimonial.role}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.section>

      {/* Contact Form Section */}
      <motion.section
        className="py-20 px-4"
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={fadeInUp}
      >
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6 tracking-tight">
              ¿Listo para tener el sitio web de tus sueños?
            </h2>
            <p className="text-xl text-slate-300 font-light leading-relaxed max-w-3xl mx-auto">
              Obtén una cotización personalizada y descubre cómo podemos crear la presencia web perfecta para tu negocio
            </p>
          </div>

          <ContactForm
            service="Creación de Páginas Web"
            title="Solicita tu cotización personalizada"
            description="Cuéntanos sobre tu proyecto web y te enviaremos una propuesta detallada"
            showBudget={true}
            showTimeline={true}
          />
        </div>
      </motion.section>
    </div>
  )
}
