"use client"

import { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Search,
  Eye,
  Phone,
  Mail,
  Calendar,
  BarChart3,
  RefreshCw,
  Loader2,
  Settings,
  Database,
  Activity,
} from "lucide-react"
import { motion } from "framer-motion"
import Link from "next/link"
import Image from "next/image"
import {
  type Contact,
  type ContactStats,
  type ContactActivity,
  getContacts,
  getContactStats,
  getContactById,
  updateContactStatus,
  addContactActivity,
} from "@/lib/database"
import { Textarea } from "@/components/ui/textarea"
import DashboardCharts from "@/components/admin/dashboard-charts"
import ContactManager from "@/components/admin/contact-manager"

// Componente principal del dashboard
export default function AdminDashboard() {
  const [contacts, setContacts] = useState<Contact[]>([])
  const [stats, setStats] = useState<ContactStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [serviceFilter, setServiceFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null)
  const [contactActivities, setContactActivities] = useState<ContactActivity[]>([])
  const [isModalLoading, setIsModalLoading] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [activeTab, setActiveTab] = useState("overview")

  const loadData = useCallback(async () => {
    setLoading(true)
    try {
      const filters = {
        status: statusFilter === "all" ? undefined : statusFilter,
        service: serviceFilter === "all" ? undefined : serviceFilter,
        priority: priorityFilter === "all" ? undefined : priorityFilter,
        search: searchTerm || undefined,
        limit: 50, // Adjust as needed, consider pagination for large datasets
        offset: 0,
      }
      const [fetchedContacts, fetchedStats] = await Promise.all([getContacts(filters), getContactStats()])

      setContacts(fetchedContacts)
      setStats(fetchedStats)
    } catch (error) {
      console.error("Error loading data:", error)
      // TODO: Show an error message to the user
    } finally {
      setLoading(false)
    }
  }, [statusFilter, serviceFilter, priorityFilter, searchTerm])

  // Cargar datos iniciales y cuando cambian los filtros
  useEffect(() => {
    loadData()
  }, [loadData])

  // Filtrar contactos (client-side, consider server-side for large datasets)
  // This client-side filtering is redundant if server-side filtering is fully implemented via `loadData`
  // For now, `getContacts` handles the filtering based on `statusFilter`, `serviceFilter`, `priorityFilter`, `searchTerm`
  const filteredContacts = contacts

  // Obtener color del badge según el estado
  const getStatusColor = (status: string) => {
    switch (status) {
      case "new":
        return "bg-blue-600/20 text-blue-400 border-blue-600/30"
      case "contacted":
        return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "in_progress":
        return "bg-purple-600/20 text-purple-400 border-purple-600/30"
      case "quoted":
        return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "closed":
        return "bg-green-600/20 text-green-400 border-green-600/30"
      case "lost":
        return "bg-red-600/20 text-red-400 border-red-600/30"
      default:
        return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  // Obtener color del badge según la prioridad
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "urgent":
        return "bg-red-600/20 text-red-400 border-red-600/30"
      case "high":
        return "bg-orange-600/20 text-orange-400 border-orange-600/30"
      case "medium":
        return "bg-yellow-600/20 text-yellow-400 border-yellow-600/30"
      case "low":
        return "bg-green-600/20 text-green-400 border-green-600/30"
      default:
        return "bg-slate-600/20 text-slate-400 border-slate-600/30"
    }
  }

  // Ver detalles del contacto
  const viewContactDetails = async (contact: Contact) => {
    setSelectedContact(contact)
    setIsModalLoading(true)
    try {
      const details = await getContactById(contact.id)
      if (details) {
        setContactActivities(details.activities)
        setSelectedContact(details.contact) // Update with potentially fresher data
      } else {
        setContactActivities([])
        // TODO: Handle case where contact details are not found
      }
    } catch (error) {
      console.error("Error fetching contact details:", error)
      setContactActivities([])
      // TODO: Show an error message
    } finally {
      setIsModalLoading(false)
    }
  }

  const handleUpdateStatus = async (contactId: number, newStatus: Contact["status"]) => {
    if (!selectedContact || selectedContact.id !== contactId) return

    setIsUpdatingStatus(true)
    try {
      await updateContactStatus(contactId, newStatus, selectedContact.notes, selectedContact.assigned_to)
      // Update local state
      setSelectedContact((prev) => (prev ? { ...prev, status: newStatus, updated_at: new Date().toISOString() } : null))
      setContacts((prevContacts) =>
        prevContacts.map((c) =>
          c.id === contactId ? { ...c, status: newStatus, updated_at: new Date().toISOString() } : c,
        ),
      )
      // Optionally, reload stats or specific contact if needed
      // await loadData(); // Or a more targeted update
    } catch (error) {
      console.error("Error updating contact status:", error)
      // TODO: Show error to user
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Función para actualizar contacto desde ContactManager
  const handleContactUpdate = async (contactId: number, updates: Partial<Contact>) => {
    try {
      await updateContactStatus(
        contactId,
        updates.status || selectedContact?.status || "new",
        updates.notes,
        updates.assigned_to
      )

      // Actualizar estado local
      setSelectedContact(prev => prev ? { ...prev, ...updates } : null)
      setContacts(prevContacts =>
        prevContacts.map(c =>
          c.id === contactId ? { ...c, ...updates, updated_at: new Date().toISOString() } : c
        )
      )

      // Recargar estadísticas
      const newStats = await getContactStats()
      setStats(newStats)
    } catch (error) {
      console.error("Error updating contact:", error)
      throw error
    }
  }

  // Función para agregar actividad desde ContactManager
  const handleAddActivity = async (contactId: number, activity: { activity_type: string; description: string; performed_by?: string }) => {
    try {
      await addContactActivity(contactId, activity.activity_type, activity.description, activity.performed_by)

      // Recargar actividades del contacto
      const contactData = await getContactById(contactId)
      if (contactData) {
        setContactActivities(contactData.activities)
      }
    } catch (error) {
      console.error("Error adding activity:", error)
      throw error
    }
  }

  if (loading && !stats) {
    // Show full page loader only on initial load
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-16 h-16 text-teal-600 animate-spin mx-auto mb-4" />
          <p className="text-white text-lg">Cargando dashboard...</p>
        </div>
      </div>
    )
  }

  const availableServices = stats?.by_service
    ? Object.keys(stats.by_service)
    : [
        "Asistencia Informática",
        "Análisis de Vulnerabilidades",
        "Consultoría en Inteligencia Artificial",
        "Creación de Páginas Web",
      ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 font-inter">
      {/* Navigation */}
      <nav className="bg-slate-900/80 backdrop-blur-md border-b border-slate-700/50 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Link href="/" className="flex items-center space-x-3 hover:scale-105 transition-transform">
            <Image src="/logo.png" alt="Karedesk" width={40} height={40} className="w-10 h-10" />
            <span className="text-2xl font-bold text-white tracking-tight">Karedesk Admin</span>
          </Link>
          <div className="flex items-center space-x-4">
            <Button
              onClick={loadData}
              variant="ghost"
              size="sm"
              className="text-slate-300 hover:text-white rounded-2xl"
              disabled={loading}
            >
              {loading && !stats ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 mr-2" />
              )}
              Actualizar
            </Button>
            <Link href="/">
              <Button variant="outline" className="rounded-2xl">
                Ver Sitio Web
              </Button>
            </Link>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-white mb-2 tracking-tight">Dashboard de Consultas</h1>
          <p className="text-slate-400 text-lg">Gestiona y da seguimiento a todas las consultas de clientes</p>
        </motion.div>

        {/* Tabs Navigation */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="bg-slate-900/50 border border-slate-700 rounded-2xl p-1">
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-teal-600 data-[state=active]:text-white text-slate-400 rounded-xl"
            >
              <BarChart3 className="w-4 h-4 mr-2" />
              Resumen
            </TabsTrigger>
            <TabsTrigger
              value="contacts"
              className="data-[state=active]:bg-teal-600 data-[state=active]:text-white text-slate-400 rounded-xl"
            >
              <Users className="w-4 h-4 mr-2" />
              Contactos
            </TabsTrigger>
            <TabsTrigger
              value="analytics"
              className="data-[state=active]:bg-teal-600 data-[state=active]:text-white text-slate-400 rounded-xl"
            >
              <Activity className="w-4 h-4 mr-2" />
              Analytics
            </TabsTrigger>
            <TabsTrigger
              value="database"
              className="data-[state=active]:bg-teal-600 data-[state=active]:text-white text-slate-400 rounded-xl"
            >
              <Database className="w-4 h-4 mr-2" />
              Base de Datos
            </TabsTrigger>
          </TabsList>

          {/* Pestaña de Resumen */}
          <TabsContent value="overview" className="space-y-6">
            {/* Estadísticas */}
            {stats && (
              <motion.div
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
              >
                <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Total Consultas</p>
                        <p className="text-3xl font-bold text-white">{stats.total}</p>
                      </div>
                      <div className="w-12 h-12 bg-teal-600/20 rounded-2xl flex items-center justify-center">
                        <Users className="w-6 h-6 text-teal-400" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
                      <span className="text-green-400">+{stats.this_month} este mes</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Nuevas</p>
                        <p className="text-3xl font-bold text-white">{stats.new}</p>
                      </div>
                      <div className="w-12 h-12 bg-blue-600/20 rounded-2xl flex items-center justify-center">
                        <AlertCircle className="w-6 h-6 text-blue-400" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <Clock className="w-4 h-4 text-yellow-400 mr-1" />
                      <span className="text-yellow-400">Requieren atención</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Tasa de Respuesta</p>
                        <p className="text-3xl font-bold text-white">{stats.response_rate}%</p>
                      </div>
                      <div className="w-12 h-12 bg-green-600/20 rounded-2xl flex items-center justify-center">
                        <CheckCircle className="w-6 h-6 text-green-400" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <BarChart3 className="w-4 h-4 text-green-400 mr-1" />
                      <span className="text-green-400">Excelente rendimiento</span>
                    </div>
                  </CardContent>
                </Card>

                <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-slate-400 text-sm font-medium">Tiempo Promedio</p>
                        <p className="text-3xl font-bold text-white">{stats.avg_response_time}h</p>
                      </div>
                      <div className="w-12 h-12 bg-purple-600/20 rounded-2xl flex items-center justify-center">
                        <Clock className="w-6 h-6 text-purple-400" />
                      </div>
                    </div>
                    <div className="mt-4 flex items-center text-sm">
                      <TrendingUp className="w-4 h-4 text-purple-400 mr-1" />
                      <span className="text-purple-400">Tiempo de respuesta</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </TabsContent>

          {/* Pestaña de Contactos */}
          <TabsContent value="contacts" className="space-y-6">
            {/* Filtros */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                        <Input
                          placeholder="Buscar por nombre, email o empresa..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10 bg-slate-800/50 border-slate-600 text-white rounded-2xl"
                        />
                      </div>
                    </div>
                    <div className="flex flex-wrap gap-4">
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-full sm:w-40 bg-slate-800/50 border-slate-600 text-white rounded-2xl">
                          <SelectValue placeholder="Estado" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-600">
                          <SelectItem value="all">Todos los estados</SelectItem>
                          {(["new", "contacted", "in_progress", "quoted", "closed", "lost"] as Contact["status"][]).map(
                            (s) => (
                              <SelectItem key={s} value={s}>
                                {s.charAt(0).toUpperCase() + s.slice(1)}
                              </SelectItem>
                            ),
                          )}
                        </SelectContent>
                      </Select>

                      <Select value={serviceFilter} onValueChange={setServiceFilter}>
                        <SelectTrigger className="w-full sm:w-48 bg-slate-800/50 border-slate-600 text-white rounded-2xl">
                          <SelectValue placeholder="Servicio" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-600">
                          <SelectItem value="all">Todos los servicios</SelectItem>
                          {availableServices.map((service) => (
                            <SelectItem key={service} value={service}>
                              {service}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>

                      <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                        <SelectTrigger className="w-full sm:w-40 bg-slate-800/50 border-slate-600 text-white rounded-2xl">
                          <SelectValue placeholder="Prioridad" />
                        </SelectTrigger>
                        <SelectContent className="bg-slate-800 border-slate-600">
                          <SelectItem value="all">Todas</SelectItem>
                          {(["urgent", "high", "medium", "low"] as Contact["priority"][]).map((p) => (
                            <SelectItem key={p} value={p}>
                              {p.charAt(0).toUpperCase() + p.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

        {/* Tabla de consultas */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <Card className="bg-slate-900/50 border-slate-700 rounded-3xl">
            <CardHeader className="p-6">
              <CardTitle className="text-white text-xl tracking-tight">
                Consultas ({loading ? "..." : filteredContacts.length})
              </CardTitle>
              <CardDescription className="text-slate-400">Lista de todas las consultas recibidas</CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                {loading && contacts.length === 0 ? (
                  <div className="flex justify-center items-center h-64">
                    <Loader2 className="w-8 h-8 text-teal-500 animate-spin" />
                  </div>
                ) : filteredContacts.length === 0 && !loading ? (
                  <div className="text-center py-10 text-slate-400">
                    No se encontraron consultas con los filtros actuales.
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow className="border-slate-700">
                        <TableHead className="text-slate-300 font-medium">Cliente</TableHead>
                        <TableHead className="text-slate-300 font-medium">Servicio</TableHead>
                        <TableHead className="text-slate-300 font-medium">Estado</TableHead>
                        <TableHead className="text-slate-300 font-medium">Prioridad</TableHead>
                        <TableHead className="text-slate-300 font-medium">Fecha</TableHead>
                        <TableHead className="text-slate-300 font-medium">Acciones</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredContacts.map((contact) => (
                        <TableRow key={contact.id} className="border-slate-700 hover:bg-slate-800/30">
                          <TableCell>
                            <div>
                              <div className="font-medium text-white">{contact.name}</div>
                              <div className="text-sm text-slate-400">{contact.email}</div>
                              {contact.company && <div className="text-sm text-slate-500">{contact.company}</div>}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-slate-300">{contact.service}</div>
                            {contact.budget && <div className="text-sm text-slate-500">${contact.budget}</div>}
                          </TableCell>
                          <TableCell>
                            <Badge className={getStatusColor(contact.status)}>{contact.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getPriorityColor(contact.priority)}>{contact.priority}</Badge>
                          </TableCell>
                          <TableCell className="text-slate-400">
                            {new Date(contact.created_at).toLocaleDateString("es-ES", {
                              year: "numeric",
                              month: "short",
                              day: "numeric",
                            })}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Dialog onOpenChange={(open) => !open && setSelectedContact(null)}>
                                <DialogTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => viewContactDetails(contact)}
                                    className="text-slate-400 hover:text-white"
                                  >
                                    <Eye className="w-4 h-4" />
                                  </Button>
                                </DialogTrigger>
                                <DialogContent className="bg-slate-900 border-slate-700 text-white max-w-2xl">
                                  <DialogHeader>
                                    <DialogTitle className="text-xl">
                                      Consulta #{selectedContact?.id} - {selectedContact?.name}
                                    </DialogTitle>
                                    <DialogDescription className="text-slate-400">
                                      Detalles completos de la consulta
                                    </DialogDescription>
                                  </DialogHeader>
                                  {isModalLoading ? (
                                    <div className="flex justify-center items-center h-40">
                                      <Loader2 className="w-8 h-8 text-teal-500 animate-spin" />
                                    </div>
                                  ) : (
                                    selectedContact && (
                                      <div className="space-y-6 py-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Email</label>
                                            <p className="text-white">{selectedContact.email}</p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Teléfono</label>
                                            <p className="text-white">{selectedContact.phone}</p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Empresa</label>
                                            <p className="text-white">{selectedContact.company || "No especificada"}</p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Servicio</label>
                                            <p className="text-white">{selectedContact.service}</p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Presupuesto</label>
                                            <p className="text-white">{selectedContact.budget || "No especificado"}</p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Plazo</label>
                                            <p className="text-white">
                                              {selectedContact.timeline || "No especificado"}
                                            </p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Prioridad</label>
                                            <p>
                                              <Badge className={getPriorityColor(selectedContact.priority)}>
                                                {selectedContact.priority}
                                              </Badge>
                                            </p>
                                          </div>
                                          <div>
                                            <label className="text-sm font-medium text-slate-400">Estado Actual</label>
                                            <Select
                                              value={selectedContact.status}
                                              onValueChange={(newStatus) =>
                                                handleUpdateStatus(selectedContact.id, newStatus as Contact["status"])
                                              }
                                              disabled={isUpdatingStatus}
                                            >
                                              <SelectTrigger className="w-full bg-slate-800/50 border-slate-600 text-white rounded-lg mt-1">
                                                <SelectValue placeholder="Cambiar estado" />
                                              </SelectTrigger>
                                              <SelectContent className="bg-slate-800 border-slate-600">
                                                {(
                                                  [
                                                    "new",
                                                    "contacted",
                                                    "in_progress",
                                                    "quoted",
                                                    "closed",
                                                    "lost",
                                                  ] as Contact["status"][]
                                                ).map((s) => (
                                                  <SelectItem key={s} value={s}>
                                                    {s.charAt(0).toUpperCase() + s.slice(1)}
                                                  </SelectItem>
                                                ))}
                                              </SelectContent>
                                            </Select>
                                            {isUpdatingStatus && (
                                              <Loader2 className="w-4 h-4 text-teal-500 animate-spin ml-2 inline-block" />
                                            )}
                                          </div>
                                        </div>

                                        <div>
                                          <label className="text-sm font-medium text-slate-400">Mensaje</label>
                                          <p className="text-white bg-slate-800/50 p-4 rounded-lg mt-1 whitespace-pre-wrap">
                                            {selectedContact.message}
                                          </p>
                                        </div>
                                        <div>
                                          <label className="text-sm font-medium text-slate-400">Notas Internas</label>
                                          <Textarea
                                            defaultValue={selectedContact.notes || ""}
                                            onBlur={(e) => {
                                              // Optimistic update locally, then save
                                              const newNotes = e.target.value
                                              setSelectedContact((prev) => (prev ? { ...prev, notes: newNotes } : null))
                                              // TODO: Debounce this and call updateContactStatus or a dedicated updateNotes function
                                              // For now, status update will carry notes if changed there.
                                            }}
                                            placeholder="Añadir notas internas..."
                                            className="bg-slate-800/50 border-slate-600 text-white rounded-lg mt-1 min-h-[80px]"
                                          />
                                        </div>

                                        <div className="flex space-x-4 pt-2">
                                          <Button
                                            size="sm"
                                            className="bg-teal-600 hover:bg-teal-700"
                                            onClick={() => window.open(`mailto:${selectedContact.email}`)}
                                          >
                                            <Mail className="w-4 h-4 mr-2" />
                                            Enviar Email
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={() => window.open(`tel:${selectedContact.phone}`)}
                                          >
                                            <Phone className="w-4 h-4 mr-2" />
                                            Llamar
                                          </Button>
                                        </div>

                                        <div>
                                          <h4 className="font-medium text-white mb-3">Historial de Actividades</h4>
                                          {contactActivities.length > 0 ? (
                                            <div className="space-y-3 max-h-48 overflow-y-auto pr-2">
                                              {contactActivities.map((activity) => (
                                                <div
                                                  key={activity.id}
                                                  className="flex items-start space-x-3 p-3 bg-slate-800/30 rounded-lg"
                                                >
                                                  <Calendar className="w-4 h-4 text-slate-400 mt-1 flex-shrink-0" />
                                                  <div className="flex-1">
                                                    <p className="text-sm text-white">{activity.description}</p>
                                                    <p className="text-xs text-slate-400">
                                                      {new Date(activity.created_at).toLocaleString("es-ES", {
                                                        dateStyle: "medium",
                                                        timeStyle: "short",
                                                      })}
                                                      {activity.performed_by && ` - ${activity.performed_by}`}
                                                    </p>
                                                  </div>
                                                </div>
                                              ))}
                                            </div>
                                          ) : (
                                            <p className="text-sm text-slate-400">
                                              No hay actividades registradas para esta consulta.
                                            </p>
                                          )}
                                        </div>
                                      </div>
                                    )
                                  )}
                                </DialogContent>
                              </Dialog>

                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`mailto:${contact.email}`)}
                                className="text-slate-400 hover:text-white"
                              >
                                <Mail className="w-4 h-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => window.open(`tel:${contact.phone}`)}
                                className="text-slate-400 hover:text-white"
                              >
                                <Phone className="w-4 h-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
