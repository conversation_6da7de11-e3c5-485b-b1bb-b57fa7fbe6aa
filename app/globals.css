@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Karedesk Brand Components */
  .btn-brand {
    @apply bg-brand-primary hover:bg-brand-secondary text-white font-semibold px-6 py-3 rounded-2xl transition-all duration-300 shadow-brand hover:shadow-brand-lg hover:scale-105 active:scale-95;
  }

  .btn-brand-outline {
    @apply border-2 border-brand-primary text-brand-primary hover:bg-brand-primary hover:text-white font-semibold px-6 py-3 rounded-2xl transition-all duration-300 hover:shadow-brand;
  }

  .btn-brand-ghost {
    @apply text-brand-primary hover:bg-brand-primary/10 font-semibold px-6 py-3 rounded-2xl transition-all duration-300;
  }

  .card-brand {
    @apply bg-slate-900/50 border border-slate-700/50 rounded-3xl backdrop-blur-sm hover:border-brand-primary/30 transition-all duration-300 hover:shadow-brand;
  }

  .input-brand {
    @apply bg-slate-800/50 border border-slate-600 text-white rounded-2xl focus:border-brand-primary focus:ring-2 focus:ring-brand-primary/20 transition-all duration-300;
  }

  .text-gradient-brand {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-brand-primary to-brand-accent;
  }

  .bg-gradient-brand {
    @apply bg-gradient-to-r from-brand-primary to-brand-accent;
  }

  /* Service Color Classes */
  .service-tech {
    @apply text-service-tech-400 bg-service-tech-500/20 border-service-tech-500/30;
  }

  .service-security {
    @apply text-service-security-400 bg-service-security-500/20 border-service-security-500/30;
  }

  .service-ai {
    @apply text-service-ai-400 bg-service-ai-500/20 border-service-ai-500/30;
  }

  .service-web {
    @apply text-service-web-400 bg-service-web-500/20 border-service-web-500/30;
  }

  /* Icon containers for services */
  .icon-tech {
    @apply bg-service-tech-500/20 text-service-tech-400;
  }

  .icon-security {
    @apply bg-service-security-500/20 text-service-security-400;
  }

  .icon-ai {
    @apply bg-service-ai-500/20 text-service-ai-400;
  }

  .icon-web {
    @apply bg-service-web-500/20 text-service-web-400;
  }

  /* Hover effects for service cards */
  .card-tech:hover {
    @apply border-service-tech-500/50 shadow-lg shadow-service-tech-500/10;
  }

  .card-security:hover {
    @apply border-service-security-500/50 shadow-lg shadow-service-security-500/10;
  }

  .card-ai:hover {
    @apply border-service-ai-500/50 shadow-lg shadow-service-ai-500/10;
  }

  .card-web:hover {
    @apply border-service-web-500/50 shadow-lg shadow-service-web-500/10;
  }

  /* Animations */
  .animate-float {
    animation: bounce-gentle 3s ease-in-out infinite;
  }

  .animate-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: #14b8a6 #1e293b;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: #1e293b;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: #14b8a6;
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: #0d9488;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .bg-grid {
    background-image: linear-gradient(rgba(20, 184, 166, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(20, 184, 166, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  .bg-dots {
    background-image: radial-gradient(rgba(20, 184, 166, 0.2) 1px, transparent 1px);
    background-size: 20px 20px;
  }
}

/* Custom focus styles */
.focus-brand:focus {
  @apply outline-none ring-2 ring-brand-primary ring-offset-2 ring-offset-slate-900;
}

/* Loading spinner */
.spinner {
  border: 3px solid rgba(20, 184, 166, 0.3);
  border-radius: 50%;
  border-top: 3px solid #14b8a6;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Selection styling */
::selection {
  background-color: rgba(20, 184, 166, 0.3);
  color: white;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
