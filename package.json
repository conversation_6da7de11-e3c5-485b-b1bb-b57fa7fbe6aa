{"name": "karedesk-website", "version": "1.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@neondatabase/serverless": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-slot": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "react": "^19", "react-dom": "^19", "resend": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "15.0.0", "postcss": "^8.5", "tailwindcss": "^3.4.0", "typescript": "^5"}}