"use server"

import { Resend } from "resend"
import type { ContactFormData } from "./contact-actions"

const resend = new Resend(process.env.RESEND_API_KEY)

// Email templates para cada servicio
const serviceTemplates = {
  "Asistencia Informática": {
    subject: "🔧 Nueva Consulta - Asistencia Informática",
    color: "#14b8a6", // teal
    icon: "🔧",
    description: "Soporte técnico especializado",
  },
  "Análisis de Vulnerabilidades": {
    subject: "🛡️ Nueva Consulta - Análisis de Vulnerabilidades",
    color: "#ef4444", // red
    icon: "🛡️",
    description: "Auditoría de seguridad",
  },
  "Consultoría en Inteligencia Artificial": {
    subject: "🧠 Nueva Consulta - Consultoría IA",
    color: "#a855f7", // purple
    icon: "🧠",
    description: "Soluciones de inteligencia artificial",
  },
  "Creación de Páginas Web": {
    subject: "💻 Nueva Consulta - Desarrollo Web",
    color: "#10b981", // emerald
    icon: "💻",
    description: "Desarrollo de sitios web",
  },
}

// Template para email al cliente (confirmación)
function generateClientEmailTemplate(data: ContactFormData) {
  const template = serviceTemplates[data.service as keyof typeof serviceTemplates]

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Confirmación de Consulta - Karedesk</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #0f172a; color: #e2e8f0;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #1e293b; border-radius: 16px; overflow: hidden; margin-top: 20px; margin-bottom: 20px;">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, ${template.color}20 0%, ${template.color}10 100%); padding: 40px 30px; text-align: center; border-bottom: 1px solid #334155;">
          <div style="font-size: 48px; margin-bottom: 16px;">${template.icon}</div>
          <h1 style="margin: 0; font-size: 28px; font-weight: 700; color: #ffffff; letter-spacing: -0.025em;">¡Gracias por contactarnos!</h1>
          <p style="margin: 12px 0 0 0; font-size: 18px; color: #94a3b8; font-weight: 300;">Hemos recibido tu consulta sobre ${template.description}</p>
        </div>

        <!-- Content -->
        <div style="padding: 40px 30px;">
          <div style="background-color: #334155; border-radius: 12px; padding: 24px; margin-bottom: 32px; border-left: 4px solid ${template.color};">
            <h2 style="margin: 0 0 16px 0; font-size: 20px; font-weight: 600; color: #ffffff;">Hola ${data.name},</h2>
            <p style="margin: 0; font-size: 16px; line-height: 1.6; color: #cbd5e1;">
              Gracias por tu interés en nuestros servicios de <strong style="color: ${template.color};">${data.service}</strong>. 
              Hemos recibido tu consulta y uno de nuestros especialistas se pondrá en contacto contigo en las próximas 24 horas.
            </p>
          </div>

          <!-- Resumen de la consulta -->
          <div style="background-color: #1e293b; border: 1px solid #334155; border-radius: 12px; padding: 24px; margin-bottom: 32px;">
            <h3 style="margin: 0 0 20px 0; font-size: 18px; font-weight: 600; color: #ffffff; border-bottom: 1px solid #334155; padding-bottom: 12px;">Resumen de tu consulta</h3>
            
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Servicio:</span>
              <span style="color: #e2e8f0; margin-left: 8px;">${data.service}</span>
            </div>
            
            ${
              data.company
                ? `
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Empresa:</span>
              <span style="color: #e2e8f0; margin-left: 8px;">${data.company}</span>
            </div>
            `
                : ""
            }
            
            ${
              data.budget
                ? `
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Presupuesto:</span>
              <span style="color: #e2e8f0; margin-left: 8px;">$${data.budget}</span>
            </div>
            `
                : ""
            }
            
            ${
              data.timeline
                ? `
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Timeline:</span>
              <span style="color: #e2e8f0; margin-left: 8px;">${data.timeline}</span>
            </div>
            `
                : ""
            }
            
            <div style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #334155;">
              <span style="font-weight: 600; color: ${template.color};">Mensaje:</span>
              <p style="color: #cbd5e1; margin: 8px 0 0 0; line-height: 1.6; font-style: italic;">"${data.message}"</p>
            </div>
          </div>

          <!-- Próximos pasos -->
          <div style="background: linear-gradient(135deg, ${template.color}15 0%, ${template.color}05 100%); border-radius: 12px; padding: 24px; margin-bottom: 32px; border: 1px solid ${template.color}30;">
            <h3 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #ffffff;">📋 Próximos pasos</h3>
            <ul style="margin: 0; padding-left: 20px; color: #cbd5e1; line-height: 1.8;">
              <li>Revisaremos tu consulta en detalle</li>
              <li>Un especialista te contactará en menos de 24 horas</li>
              <li>Programaremos una consulta gratuita personalizada</li>
              <li>Te enviaremos una propuesta detallada</li>
            </ul>
          </div>

          <!-- Contacto de emergencia -->
          <div style="text-align: center; padding: 24px; background-color: #334155; border-radius: 12px;">
            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #ffffff;">¿Necesitas contactarnos urgentemente?</h3>
            <p style="margin: 0 0 16px 0; color: #94a3b8;">Puedes escribirnos directamente:</p>
            <a href="mailto:<EMAIL>" style="display: inline-block; background-color: ${template.color}; color: white; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; margin: 0 8px;">📧 <EMAIL></a>
            <a href="tel:+15551234567" style="display: inline-block; background-color: transparent; color: ${template.color}; padding: 12px 24px; text-decoration: none; border-radius: 8px; font-weight: 600; border: 2px solid ${template.color}; margin: 0 8px;">📞 +****************</a>
          </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #0f172a; padding: 30px; text-align: center; border-top: 1px solid #334155;">
          <div style="margin-bottom: 16px;">
            <img src="https://via.placeholder.com/40x40/14b8a6/ffffff?text=K" alt="Karedesk" style="width: 40px; height: 40px; border-radius: 8px;">
          </div>
          <h4 style="margin: 0 0 8px 0; font-size: 20px; font-weight: 700; color: #ffffff;">Karedesk</h4>
          <p style="margin: 0 0 16px 0; color: #64748b; font-size: 14px;">Transformando el futuro digital</p>
          <p style="margin: 0; color: #475569; font-size: 12px;">
            © 2024 Karedesk. Todos los derechos reservados.<br>
            Este email fue enviado porque solicitaste información sobre nuestros servicios.
          </p>
        </div>
      </div>
    </body>
    </html>
  `
}

// Template para email interno (notificación al equipo)
function generateInternalEmailTemplate(data: ContactFormData) {
  const template = serviceTemplates[data.service as keyof typeof serviceTemplates]

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Nueva Consulta - ${data.service}</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background-color: #f8fafc; color: #1e293b;">
      <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 12px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
        
        <!-- Header -->
        <div style="background: linear-gradient(135deg, ${template.color} 0%, ${template.color}dd 100%); padding: 30px; text-align: center;">
          <div style="font-size: 36px; margin-bottom: 12px;">${template.icon}</div>
          <h1 style="margin: 0; font-size: 24px; font-weight: 700; color: #ffffff;">Nueva Consulta Recibida</h1>
          <p style="margin: 8px 0 0 0; font-size: 16px; color: #ffffff; opacity: 0.9;">${data.service}</p>
        </div>

        <!-- Información del cliente -->
        <div style="padding: 30px;">
          <div style="background-color: #f1f5f9; border-radius: 8px; padding: 20px; margin-bottom: 24px;">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #1e293b;">👤 Información del Cliente</h2>
            
            <div style="display: grid; gap: 12px;">
              <div>
                <span style="font-weight: 600; color: ${template.color};">Nombre:</span>
                <span style="margin-left: 8px; color: #475569;">${data.name}</span>
              </div>
              
              <div>
                <span style="font-weight: 600; color: ${template.color};">Email:</span>
                <a href="mailto:${data.email}" style="margin-left: 8px; color: #0ea5e9; text-decoration: none;">${data.email}</a>
              </div>
              
              <div>
                <span style="font-weight: 600; color: ${template.color};">Teléfono:</span>
                <a href="tel:${data.phone}" style="margin-left: 8px; color: #0ea5e9; text-decoration: none;">${data.phone}</a>
              </div>
              
              ${
                data.company
                  ? `
              <div>
                <span style="font-weight: 600; color: ${template.color};">Empresa:</span>
                <span style="margin-left: 8px; color: #475569;">${data.company}</span>
              </div>
              `
                  : ""
              }
            </div>
          </div>

          <!-- Detalles del proyecto -->
          <div style="background-color: #fefefe; border: 1px solid #e2e8f0; border-radius: 8px; padding: 20px; margin-bottom: 24px;">
            <h2 style="margin: 0 0 16px 0; font-size: 18px; font-weight: 600; color: #1e293b;">📋 Detalles del Proyecto</h2>
            
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Servicio solicitado:</span>
              <span style="margin-left: 8px; color: #475569;">${data.service}</span>
            </div>
            
            ${
              data.budget
                ? `
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Presupuesto estimado:</span>
              <span style="margin-left: 8px; color: #475569; background-color: #dcfce7; padding: 4px 8px; border-radius: 4px; font-weight: 600;">$${data.budget}</span>
            </div>
            `
                : ""
            }
            
            ${
              data.timeline
                ? `
            <div style="margin-bottom: 16px;">
              <span style="font-weight: 600; color: ${template.color};">Timeline preferido:</span>
              <span style="margin-left: 8px; color: #475569; background-color: #fef3c7; padding: 4px 8px; border-radius: 4px; font-weight: 600;">${data.timeline}</span>
            </div>
            `
                : ""
            }
            
            <div style="margin-top: 20px; padding-top: 16px; border-top: 1px solid #e2e8f0;">
              <span style="font-weight: 600; color: ${template.color};">Mensaje del cliente:</span>
              <div style="background-color: #f8fafc; border-left: 4px solid ${template.color}; padding: 16px; margin-top: 8px; border-radius: 0 8px 8px 0;">
                <p style="margin: 0; color: #475569; line-height: 1.6; font-style: italic;">"${data.message}"</p>
              </div>
            </div>
          </div>

          <!-- Acciones rápidas -->
          <div style="background: linear-gradient(135deg, ${template.color}10 0%, ${template.color}05 100%); border-radius: 8px; padding: 20px; text-align: center;">
            <h3 style="margin: 0 0 16px 0; font-size: 16px; font-weight: 600; color: #1e293b;">⚡ Acciones Rápidas</h3>
            <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
              <a href="mailto:${data.email}" style="display: inline-block; background-color: ${template.color}; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 14px;">📧 Responder Email</a>
              <a href="tel:${data.phone}" style="display: inline-block; background-color: #10b981; color: white; padding: 10px 20px; text-decoration: none; border-radius: 6px; font-weight: 600; font-size: 14px;">📞 Llamar Cliente</a>
            </div>
            <p style="margin: 16px 0 0 0; color: #64748b; font-size: 12px;">
              ⏰ Consulta recibida el ${new Date().toLocaleDateString("es-ES", {
                weekday: "long",
                year: "numeric",
                month: "long",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
              })}
            </p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `
}

export async function sendContactEmails(data: ContactFormData) {
  try {
    const template = serviceTemplates[data.service as keyof typeof serviceTemplates]

    // Email de confirmación al cliente
    const clientEmailResult = await resend.emails.send({
      from: "Karedesk <<EMAIL>>",
      to: [data.email],
      subject: `✅ Confirmación de consulta - ${data.service}`,
      html: generateClientEmailTemplate(data),
    })

    // Email de notificación al equipo
    const internalEmailResult = await resend.emails.send({
      from: "Sistema Karedesk <<EMAIL>>",
      to: ["<EMAIL>", "<EMAIL>"], // Agregar emails del equipo
      subject: template.subject,
      html: generateInternalEmailTemplate(data),
    })

    console.log("Emails enviados exitosamente:", {
      clientEmail: clientEmailResult.data?.id,
      internalEmail: internalEmailResult.data?.id,
    })

    return {
      success: true,
      clientEmailId: clientEmailResult.data?.id,
      internalEmailId: internalEmailResult.data?.id,
    }
  } catch (error) {
    console.error("Error enviando emails:", error)

    // Log detallado del error para debugging
    if (error instanceof Error) {
      console.error("Error details:", {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })
    }

    throw new Error("Error al enviar los emails de confirmación")
  }
}

// Función para validar configuración de Resend
export async function validateEmailService() {
  try {
    if (!process.env.RESEND_API_KEY) {
      throw new Error("RESEND_API_KEY no está configurada")
    }

    // Test básico de conectividad
    const testResult = await resend.emails.send({
      from: "<EMAIL>",
      to: ["<EMAIL>"],
      subject: "Test de conectividad",
      html: "<p>Test email</p>",
    })

    return { success: true, message: "Servicio de email configurado correctamente" }
  } catch (error) {
    console.error("Error validando servicio de email:", error)
    return { success: false, error: error instanceof Error ? error.message : "Error desconocido" }
  }
}
