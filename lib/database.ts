"use server"

import { neon } from "@neondatabase/serverless"

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL is not defined")
}

const sql = neon(process.env.DATABASE_URL)

export interface Contact {
  id: number
  name: string
  email: string
  phone: string
  company?: string
  service: string
  message: string
  budget?: string
  timeline?: string
  status: "new" | "contacted" | "in_progress" | "quoted" | "closed" | "lost"
  priority: "low" | "medium" | "high" | "urgent"
  assigned_to?: string
  notes?: string
  client_email_id?: string
  internal_email_id?: string
  created_at: string
  updated_at: string
  responded_at?: string
  closed_at?: string
}

export interface ContactActivity {
  id: number
  contact_id: number
  activity_type: string
  description: string
  performed_by?: string
  created_at: string
}

export interface ContactStats {
  total: number
  new: number
  in_progress: number
  closed: number
  response_rate: number
  avg_response_time: number
  this_month: number
  this_week: number
  by_service: Record<string, number>
  by_status: Record<string, number>
}

// Guardar nueva consulta en la base de datos
export async function saveContact(contactData: {
  name: string
  email: string
  phone: string
  company?: string
  service: string
  message: string
  budget?: string
  timeline?: string
  client_email_id?: string
  internal_email_id?: string
}): Promise<Contact> {
  try {
    // Determinar prioridad basada en presupuesto
    let priority: Contact["priority"] = "medium"
    if (contactData.budget) {
      const budgetValue = contactData.budget.toLowerCase()
      if (budgetValue.includes("25000+") || budgetValue.includes("25000")) {
        priority = "high"
      } else if (budgetValue.includes("10000-25000")) {
        priority = "high"
      } else if (budgetValue.includes("1000-5000")) {
        priority = "medium"
      }
    }

    // Determinar prioridad por timeline
    if (contactData.timeline === "inmediato") {
      priority = "urgent"
    }

    const result = await sql`
      INSERT INTO contacts (
        name, email, phone, company, service, message, budget, timeline, 
        priority, client_email_id, internal_email_id
      ) VALUES (
        ${contactData.name}, ${contactData.email}, ${contactData.phone}, 
        ${contactData.company || null}, ${contactData.service}, ${contactData.message},
        ${contactData.budget || null}, ${contactData.timeline || null},
        ${priority}, ${contactData.client_email_id || null}, ${contactData.internal_email_id || null}
      ) RETURNING *
    `

    const contact = result[0] as Contact

    // Registrar actividad inicial
    await addContactActivity(
      contact.id,
      "contact_created",
      "Nueva consulta recibida desde el sitio web",
      "<EMAIL>",
    )

    return contact
  } catch (error) {
    console.error("Error saving contact:", error)
    throw new Error("Error al guardar la consulta en la base de datos")
  }
}

// Obtener todas las consultas con filtros
export async function getContacts(
  filters: {
    status?: string
    service?: string
    priority?: string
    search?: string
    limit?: number
    offset?: number
  } = {},
): Promise<Contact[]> {
  try {
    let query = `
      SELECT * FROM contacts 
      WHERE 1=1
    `
    const params: any[] = []
    let paramIndex = 1

    if (filters.status) {
      query += ` AND status = $${paramIndex}`
      params.push(filters.status)
      paramIndex++
    }

    if (filters.service) {
      query += ` AND service = $${paramIndex}`
      params.push(filters.service)
      paramIndex++
    }

    if (filters.priority) {
      query += ` AND priority = $${paramIndex}`
      params.push(filters.priority)
      paramIndex++
    }

    if (filters.search) {
      query += ` AND (name ILIKE $${paramIndex} OR email ILIKE $${paramIndex} OR company ILIKE $${paramIndex})`
      params.push(`%${filters.search}%`)
      paramIndex++
    }

    query += ` ORDER BY 
      CASE priority 
        WHEN 'urgent' THEN 1 
        WHEN 'high' THEN 2 
        WHEN 'medium' THEN 3 
        WHEN 'low' THEN 4 
      END,
      created_at DESC
    `

    if (filters.limit) {
      query += ` LIMIT $${paramIndex}`
      params.push(filters.limit)
      paramIndex++
    }

    if (filters.offset) {
      query += ` OFFSET $${paramIndex}`
      params.push(filters.offset)
    }

    const result = await sql(query, params)
    return result as Contact[]
  } catch (error) {
    console.error("Error fetching contacts:", error)
    throw new Error("Error al obtener las consultas")
  }
}

// Obtener una consulta específica con sus actividades
export async function getContactById(id: number): Promise<{ contact: Contact; activities: ContactActivity[] } | null> {
  try {
    const contactResult = await sql`
      SELECT * FROM contacts WHERE id = ${id}
    `

    if (contactResult.length === 0) {
      return null
    }

    const activitiesResult = await sql`
      SELECT * FROM contact_activities 
      WHERE contact_id = ${id} 
      ORDER BY created_at DESC
    `

    return {
      contact: contactResult[0] as Contact,
      activities: activitiesResult as ContactActivity[],
    }
  } catch (error) {
    console.error("Error fetching contact by ID:", error)
    throw new Error("Error al obtener la consulta")
  }
}

// Actualizar estado de una consulta
export async function updateContactStatus(
  id: number,
  status: Contact["status"],
  notes?: string,
  assigned_to?: string,
): Promise<void> {
  try {
    await sql`
      UPDATE contacts 
      SET status = ${status}, 
          notes = ${notes || null},
          assigned_to = ${assigned_to || null},
          responded_at = ${status === "contacted" ? new Date().toISOString() : null},
          closed_at = ${status === "closed" ? new Date().toISOString() : null}
      WHERE id = ${id}
    `

    // Registrar actividad
    await addContactActivity(
      id,
      "status_changed",
      `Estado cambiado a: ${status}`,
      assigned_to || "<EMAIL>",
    )
  } catch (error) {
    console.error("Error updating contact status:", error)
    throw new Error("Error al actualizar el estado de la consulta")
  }
}

// Agregar actividad a una consulta
export async function addContactActivity(
  contactId: number,
  activityType: string,
  description: string,
  performedBy?: string,
): Promise<void> {
  try {
    await sql`
      INSERT INTO contact_activities (contact_id, activity_type, description, performed_by)
      VALUES (${contactId}, ${activityType}, ${description}, ${performedBy || null})
    `
  } catch (error) {
    console.error("Error adding contact activity:", error)
    throw new Error("Error al registrar la actividad")
  }
}

// Obtener estadísticas de consultas
export async function getContactStats(): Promise<ContactStats> {
  try {
    // Estadísticas básicas
    const totalResult = await sql`SELECT COUNT(*) as count FROM contacts`
    const total = Number.parseInt(totalResult[0].count)

    const statusResult = await sql`
      SELECT status, COUNT(*) as count 
      FROM contacts 
      GROUP BY status
    `

    const serviceResult = await sql`
      SELECT service, COUNT(*) as count 
      FROM contacts 
      GROUP BY service
    `

    const thisMonthResult = await sql`
      SELECT COUNT(*) as count 
      FROM contacts 
      WHERE created_at >= DATE_TRUNC('month', CURRENT_DATE)
    `

    const thisWeekResult = await sql`
      SELECT COUNT(*) as count 
      FROM contacts 
      WHERE created_at >= DATE_TRUNC('week', CURRENT_DATE)
    `

    // Calcular tiempo promedio de respuesta
    const responseTimeResult = await sql`
      SELECT AVG(EXTRACT(EPOCH FROM (responded_at - created_at))/3600) as avg_hours
      FROM contacts 
      WHERE responded_at IS NOT NULL
    `

    // Calcular tasa de respuesta
    const respondedResult = await sql`
      SELECT COUNT(*) as count 
      FROM contacts 
      WHERE responded_at IS NOT NULL
    `

    const byStatus = statusResult.reduce((acc: Record<string, number>, row: any) => {
      acc[row.status] = Number.parseInt(row.count)
      return acc
    }, {})

    const byService = serviceResult.reduce((acc: Record<string, number>, row: any) => {
      acc[row.service] = Number.parseInt(row.count)
      return acc
    }, {})

    const responded = Number.parseInt(respondedResult[0].count)
    const responseRate = total > 0 ? (responded / total) * 100 : 0
    const avgResponseTime = Number.parseFloat(responseTimeResult[0].avg_hours) || 0

    return {
      total,
      new: byStatus.new || 0,
      in_progress: byStatus.in_progress || 0,
      closed: byStatus.closed || 0,
      response_rate: Math.round(responseRate * 100) / 100,
      avg_response_time: Math.round(avgResponseTime * 100) / 100,
      this_month: Number.parseInt(thisMonthResult[0].count),
      this_week: Number.parseInt(thisWeekResult[0].count),
      by_service: byService,
      by_status: byStatus,
    }
  } catch (error) {
    console.error("Error fetching contact stats:", error)
    throw new Error("Error al obtener las estadísticas")
  }
}

// Buscar consultas duplicadas por email
export async function findDuplicateContacts(email: string): Promise<Contact[]> {
  try {
    const result = await sql`
      SELECT * FROM contacts 
      WHERE email = ${email} 
      ORDER BY created_at DESC
    `
    return result as Contact[]
  } catch (error) {
    console.error("Error finding duplicate contacts:", error)
    return []
  }
}

// Obtener consultas que necesitan seguimiento
export async function getContactsNeedingFollowup(): Promise<Contact[]> {
  try {
    const result = await sql`
      SELECT * FROM contacts 
      WHERE status IN ('new', 'contacted') 
      AND created_at < NOW() - INTERVAL '24 hours'
      AND (responded_at IS NULL OR responded_at < NOW() - INTERVAL '72 hours')
      ORDER BY priority DESC, created_at ASC
    `
    return result as Contact[]
  } catch (error) {
    console.error("Error fetching contacts needing followup:", error)
    return []
  }
}
